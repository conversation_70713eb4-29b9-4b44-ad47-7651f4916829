namespace MaoYouJi
{
    public static partial class GuildInfoSystem
    {
        [EntitySystem]
        public class Mao<PERSON><PERSON><PERSON>i_GuildInfo_string_MaoYouJi_User_AwakeSystem: AwakeSystem<MaoYouJi.GuildInfo, string, MaoYouJi.User>
        {   
            protected override void Awake(MaoYouJi.GuildInfo self, string guildName, MaoYouJi.User creator)
            {
                self.Awake(guild<PERSON><PERSON>, creator);
            }
        }
    }
}