using System.Collections.Generic;
using MongoDB.Bson;

namespace MaoYouJi
{
  [EnableClass]
  public class ForgingRecipe // 锻造配方，主要用来锻造装备
  {
    public ObjectId id;
    public ForgingRecipeIdEnum recipeId;
    public int needForginLevel; // 需要的锻造等级
    public string name;
    public List<ThingGiveInfo> needThings;
    public List<ThingGiveInfo> giveThings;
    public int expReward = 10; // 完成配方获得的熟练度奖励
  }

  [EnableClass]
  public class CraftingRecipe // 合成配方，主要用来合成各种食物和药剂
  {
    public ObjectId id;
    public CraftingRecipeIdEnum recipeId;
    public int needCraftingLevel; // 需要的合成等级
    public string name;
    public List<ThingGiveInfo> needThings;
    public List<ThingGiveInfo> giveThings;
    public int expReward = 10; // 完成配方获得的熟练度奖励
  }
}