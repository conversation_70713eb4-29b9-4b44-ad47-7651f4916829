using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  // 用户锻造组件
  [ComponentOf(typeof(User))]
  public partial class UserForgingComponent : Entity, IAwake, ISerializeToEntity
  {
    // 已学会的锻造配方
    public HashSet<ForgingRecipeIdEnum> learnedRecipes { get; set; } = new HashSet<ForgingRecipeIdEnum>();
  }

  // 用户合成组件
  [ComponentOf(typeof(User))]
  public partial class UserCraftingComponent : Entity, IAwake, ISerializeToEntity
  {
    // 已学会的合成配方
    public HashSet<CraftingRecipeIdEnum> learnedRecipes { get; set; } = new HashSet<CraftingRecipeIdEnum>();
  }
}