using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class Treasure : Thing
  {
    // 记忆羽毛的宝物属性
    public int maxPointNum = 1;
    public List<PointInfo> cityPoints;
    public List<PointInfo> outPoints;
    // 给予的物品，用于礼盒或者宝箱
    public List<ThingGiveInfo> giveInfos;
    // 背包
    public int capacity; // 背包容量
    public string bagName; // 背包名称
    public List<Thing> thingsInBag = new List<Thing>(); // 背包中的物品

    // 战斗中使用的宝物属性，施加给敌人的状态或自己的状态
    [MemoryPackIgnore]
    public List<AttachStatus> targetStatus;
    [MemoryPackIgnore]
    public List<AttachStatus> selfStatus;
    // 召唤的怪物
    [MemoryPackIgnore]
    public MonBaseType callMon;
    [MemoryPackIgnore]
    public CraftingRecipeIdEnum craftingRecipeId;
    [MemoryPackIgnore]
    public ForgingRecipeIdEnum forgingRecipeId;
    public override object Clone()
    {
      Treasure treasure = (Treasure)base.Clone();
      return treasure;
    }
  }
}