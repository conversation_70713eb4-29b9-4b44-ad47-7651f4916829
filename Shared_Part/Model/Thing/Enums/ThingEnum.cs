namespace MaoYouJi
{
  public enum ThingType
  {
    None, // ("无")
    [EnumDescription("食品")]
    FOOD, // ("食品")
    [EnumDescription("装备")]
    EQUIP, // ("装备")
    [EnumDescription("材料")]
    MATERIAL, // ("材料")
    [EnumDescription("宝物")]
    TREASURE, // ("宝物")
    [EnumDescription("骑宠")]
    RODE, // ("骑宠")
    [EnumDescription("采集")]
    MINE, // ("采集")
    [EnumDescription("任务")]
    TASK, // ("任务")
  }

  public enum OwnType
  {
    None,
    PRIVATE, // ("角色独享，无法交易")  
    SHARE, //("账户共享，无法交易"),
    PUBLIC, // ("账户共享，可以交易");
  }

  public enum ThingGrade
  {
    None,
    NORMAL, // ("普通")
    GOOD, // ("良好")
    EXCELLENT, // ("优秀")
    LEGEND, // ("传说")
    MAGIC, // ("神话")
  }

  public enum ThingSubType
  {
    None, // ("无")
    Special_Equip_Recover, // ("特殊恢复装备")
    Special_Equip_Recover_Blue, // ("特殊回蓝装备")
    Special_Equip_Hufu, // ("特殊装备护符")
    Special_Equip_Rode, // ("特殊装备坐骑")
    ChuJi_ShengJiShi, // ("初级升级石")
    ShengJiShi, // ("升级石")
    GaoJi_ShengJiShi, // ("高级升级石")
    Li_He, // ("礼盒")
    Bao_Xiang, // ("宝箱")
    Bag, // ("包裹")
    HunPo, // ("魂魄")
    ChongWu_Dan, // ("宠物蛋")
    YueGuangDe_ZhuFu, // ("月光的祝福")
    QiangHua_ZhuFu, // ("强化祝福")
    CiFu_GuangHui, // ("赐福光环")
    ChongWu_ZhuanZhuan, // ("宠物转转蛋")
    JiNeng_JingYanShu, // ("技能经验书")
    CaiKuang_JiNeng_JingYanShu, // ("采矿技能经验书")
    JingYanShu, // ("经验书")
    ZhaoHuanFu, // ("召唤符")
    DaTaoSha_FightTreasure, // ("大逃杀战斗使用的物品")
    Crafting_Recipe, // ("制作配方")
    Forging_Recipe, // ("锻造配方")
  }

  public enum FoodAddType
  {
    ADD_HP, // ("回血"),
    ADD_SP, // ("回蓝"),
    ADD_ALL, // ("回血回蓝"),
    ADD_EXP, // ("增加经验"),
    OTHER, // ("其它");
  }

  public enum GemType
  {
    None,
    Attack_Gem, // ("烈焰宝石"),
    Defense_Gem, // ("坚盾宝石"),
    Magic_Defense_Gem, // ("魔御宝石"),
    Hit_Gem, // ("精准宝石"),
    Miss_Gem, // ("灵动宝石"),
    Crit_Gem, // ("狂暴宝石"),
    Blood_Gem, // ("生命宝石"),
    Blue_Gem, // ("魔力宝石"),
  }

  public enum BagType
  {
    Bag, // ("角色背包"),
    BackPack, // ("便携背包"),
    PublicStore, // ("账户仓库");
  }
}