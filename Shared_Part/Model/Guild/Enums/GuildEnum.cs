using System.ComponentModel;

namespace MaoYouJi
{
  /// <summary>
  /// 公会角色枚举
  /// </summary>
  public enum GuildRole
  {
    [Description("会员")]
    Member = 1,
    
    [Description("管理员")]
    Manager = 2,
    
    [Description("副会长")]
    VicePresident = 3,
    
    [Description("会长")]
    President = 4
  }

  /// <summary>
  /// 公会邀请状态
  /// </summary>
  public enum GuildInviteStatus
  {
    [Description("待处理")]
    Pending = 0,
    
    [Description("已接受")]
    Accepted = 1,
    
    [Description("已拒绝")]
    Rejected = 2,
    
    [Description("已过期")]
    Expired = 3
  }

  /// <summary>
  /// 公会联盟状态
  /// </summary>
  public enum GuildAllianceStatus
  {
    [Description("待处理")]
    Pending = 0,
    
    [Description("已建立")]
    Established = 1,
    
    [Description("已解除")]
    Dissolved = 2
  }

  /// <summary>
  /// 公会操作类型
  /// </summary>
  public enum GuildOperationType
  {
    [Description("创建公会")]
    Create = 1,
    
    [Description("加入公会")]
    Join = 2,
    
    [Description("离开公会")]
    Leave = 3,
    
    [Description("被踢出公会")]
    Kicked = 4,
    
    [Description("角色变更")]
    RoleChanged = 5,
    
    [Description("公会解散")]
    Disbanded = 6
  }
}
