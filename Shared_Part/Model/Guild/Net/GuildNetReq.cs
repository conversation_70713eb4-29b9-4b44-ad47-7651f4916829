using MemoryPack;

namespace <PERSON>YouJi
{
  /// <summary>
  /// 创建公会请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateGuildReq)]
  public partial class CreateGuildReq : MaoYouInMessage, ILocationRequest
  {
    public string guildName { get; set; } = ""; // 公会名称
    public string description { get; set; } = ""; // 公会描述
  }

  /// <summary>
  /// 查看我的公会请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMyGuildReq)]
  public partial class ShowMyGuildReq : MaoYouInMessage, ILocationRequest
  {
  }

  /// <summary>
  /// 编辑公会信息请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.EditGuildReq)]
  public partial class EditGuildReq : MaoYouInMessage, ILocationRequest
  {
    public string guildName { get; set; } // 公会名称（可选）
    public string description { get; set; } // 公会描述（可选）
    public string announcement { get; set; } // 公会公告（可选）
  }

  /// <summary>
  /// 解散公会请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.DisbandGuildReq)]
  public partial class DisbandGuildReq : MaoYouInMessage, ILocationRequest
  {
  }

  /// <summary>
  /// 转让会长请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.TransferGuildLeaderReq)]
  public partial class TransferGuildLeaderReq : MaoYouInMessage, ILocationRequest
  {
    public long newLeaderId { get; set; } // 新会长ID
  }

  /// <summary>
  /// 邀请公会成员请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.InviteGuildMemberReq)]
  public partial class InviteGuildMemberReq : MaoYouInMessage, ILocationRequest
  {
    public long targetUserId { get; set; } // 目标用户ID（可选）
    public string targetUserName { get; set; } = ""; // 目标用户名（可选）
    public string inviteMessage { get; set; } = ""; // 邀请消息
  }

  /// <summary>
  /// 接受公会邀请请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AcceptGuildInviteReq)]
  public partial class AcceptGuildInviteReq : MaoYouInMessage, ILocationRequest
  {
    public long inviteId { get; set; } // 邀请ID
  }

  /// <summary>
  /// 拒绝公会邀请请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RejectGuildInviteReq)]
  public partial class RejectGuildInviteReq : MaoYouInMessage, ILocationRequest
  {
    public long inviteId { get; set; } // 邀请ID
  }

  /// <summary>
  /// 踢出公会成员请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.KickGuildMemberReq)]
  public partial class KickGuildMemberReq : MaoYouInMessage, ILocationRequest
  {
    public long targetUserId { get; set; } // 目标用户ID
    public string reason { get; set; } = ""; // 踢出原因
  }

  /// <summary>
  /// 变更公会成员角色请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ChangeGuildMemberRoleReq)]
  public partial class ChangeGuildMemberRoleReq : MaoYouInMessage, ILocationRequest
  {
    public long targetUserId { get; set; } // 目标用户ID
    public GuildRole newRole { get; set; } // 新角色
  }

  /// <summary>
  /// 离开公会请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.LeaveGuildReq)]
  public partial class LeaveGuildReq : MaoYouInMessage, ILocationRequest
  {
  }

  /// <summary>
  /// 创建公会联盟请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateGuildAllianceReq)]
  public partial class CreateGuildAllianceReq : MaoYouInMessage, ILocationRequest
  {
    public long targetGuildId { get; set; } // 目标公会ID（可选）
    public string targetGuildName { get; set; } = ""; // 目标公会名称（可选）
    public string inviteMessage { get; set; } = ""; // 邀请消息
  }

  /// <summary>
  /// 接受公会联盟请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AcceptGuildAllianceReq)]
  public partial class AcceptGuildAllianceReq : MaoYouInMessage, ILocationRequest
  {
    public long inviteId { get; set; } // 邀请ID
  }

  /// <summary>
  /// 拒绝公会联盟请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RejectGuildAllianceReq)]
  public partial class RejectGuildAllianceReq : MaoYouInMessage, ILocationRequest
  {
    public long inviteId { get; set; } // 邀请ID
  }

  /// <summary>
  /// 解除公会联盟请求
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RemoveGuildAllianceReq)]
  public partial class RemoveGuildAllianceReq : MaoYouInMessage, ILocationRequest
  {
    public long targetGuildId { get; set; } // 目标公会ID
  }
}
