using MemoryPack;

namespace MaoYouJi
{
  /// <summary>
  /// 服务器更新公会信息消息
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateGuildMsg)]
  public partial class ServerUpdateGuildMsg : MessageObject
  {
    public GuildDaoInfo guildDaoInfo { get; set; } // 更新的公会信息
  }

  /// <summary>
  /// 服务器公会邀请消息
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGuildInviteMsg)]
  public partial class ServerGuildInviteMsg : MessageObject
  {
    public GuildInviteDaoInfo inviteInfo { get; set; } // 邀请信息
  }

  /// <summary>
  /// 服务器公会联盟邀请消息
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGuildAllianceInviteMsg)]
  public partial class ServerGuildAllianceInviteMsg : MessageObject
  {
    public GuildAllianceInviteDaoInfo allianceInviteInfo { get; set; } // 联盟邀请信息
  }

  /// <summary>
  /// 服务器公会成员加入消息
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGuildMemberJoinMsg)]
  public partial class ServerGuildMemberJoinMsg : MessageObject
  {
    public long guildId { get; set; } // 公会ID
    public GuildMemberInfo memberInfo { get; set; } // 新成员信息
    public string joinMessage { get; set; } = ""; // 加入消息
  }

  /// <summary>
  /// 服务器公会成员离开消息
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGuildMemberLeaveMsg)]
  public partial class ServerGuildMemberLeaveMsg : MessageObject
  {
    public long guildId { get; set; } // 公会ID
    public long userId { get; set; } // 离开的用户ID
    public string userName { get; set; } = ""; // 离开的用户名
    public GuildOperationType operationType { get; set; } // 操作类型
    public string leaveMessage { get; set; } = ""; // 离开消息
  }

  /// <summary>
  /// 服务器公会解散消息
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerGuildDisbandMsg)]
  public partial class ServerGuildDisbandMsg : MessageObject
  {
    public long guildId { get; set; } // 公会ID
    public string guildName { get; set; } = ""; // 公会名称
    public string disbandMessage { get; set; } = ""; // 解散消息
  }
}
