using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  /// <summary>
  /// 公会数据传输对象 - 用于网络传输
  /// </summary>
  [MemoryPackable]
  [EnableClass]
  public partial class GuildDaoInfo
  {
    public long guildId { get; set; } = 0; // 公会ID
    public string name { get; set; } = ""; // 公会名称
    public string description { get; set; } = ""; // 公会描述
    public string announcement { get; set; } = ""; // 公会公告
    public long leaderId { get; set; } = 0; // 会长ID
    public int level { get; set; } = 1; // 公会等级
    public int maxMembers { get; set; } = 50; // 最大成员数
    public long createTime { get; set; } = 0; // 创建时间
    public long updateTime { get; set; } = 0; // 更新时间
    public Dictionary<long, GuildMemberInfo> memberInfos { get; set; } = new(); // 成员信息
    public Dictionary<long, GuildAllianceInfo> allianceInfos { get; set; } = new(); // 联盟信息
  }

  /// <summary>
  /// 公会邀请传输对象
  /// </summary>
  [MemoryPackable]
  [EnableClass]
  public partial class GuildInviteDaoInfo
  {
    public GuildDaoInfo guildDaoInfo { get; set; } // 公会信息
    public string inviteMessage { get; set; } = ""; // 邀请消息
    public string inviterName { get; set; } = ""; // 邀请者名称
    public long inviteTime { get; set; } // 邀请时间
    public long expireTime { get; set; } // 过期时间
  }

  /// <summary>
  /// 公会联盟邀请传输对象
  /// </summary>
  [MemoryPackable]
  [EnableClass]
  public partial class GuildAllianceInviteDaoInfo
  {
    public GuildDaoInfo fromGuildInfo { get; set; } // 发起方公会信息
    public GuildDaoInfo toGuildInfo { get; set; } // 目标公会信息
    public string inviteMessage { get; set; } = ""; // 邀请消息
    public string inviterName { get; set; } = ""; // 邀请者名称
    public long inviteTime { get; set; } // 邀请时间
    public long expireTime { get; set; } // 过期时间
  }

  /// <summary>
  /// 简化的公会信息 - 用于列表显示
  /// </summary>
  [MemoryPackable]
  [EnableClass]
  public partial class SimpleGuildInfo
  {
    public long guildId { get; set; } = 0; // 公会ID
    public string name { get; set; } = ""; // 公会名称
    public string description { get; set; } = ""; // 公会描述
    public int level { get; set; } = 1; // 公会等级
    public int memberCount { get; set; } = 0; // 成员数量
    public int maxMembers { get; set; } = 50; // 最大成员数
    public string leaderName { get; set; } = ""; // 会长名称
    public long createTime { get; set; } = 0; // 创建时间
  }
}
