using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  /// <summary>
  /// 创建公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateGuildResp)]
  public partial class CreateGuildResp : MaoYouOutMessage, ILocationResponse
  {
    public GuildDaoInfo guildDaoInfo { get; set; } // 创建的公会信息
  }

  /// <summary>
  /// 查看我的公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMyGuildResp)]
  public partial class ShowMyGuildResp : MaoYouOutMessage, ILocationResponse
  {
    public GuildDaoInfo guildDaoInfo { get; set; } // 公会信息
  }

  /// <summary>
  /// 编辑公会信息响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.EditGuildResp)]
  public partial class EditGuildResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 解散公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.DisbandGuildResp)]
  public partial class DisbandGuildResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 转让会长响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.TransferGuildLeaderResp)]
  public partial class TransferGuildLeaderResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 邀请公会成员响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.InviteGuildMemberResp)]
  public partial class InviteGuildMemberResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 接受公会邀请响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AcceptGuildInviteResp)]
  public partial class AcceptGuildInviteResp : MaoYouOutMessage, ILocationResponse
  {
    public GuildDaoInfo guildDaoInfo { get; set; } // 加入的公会信息
  }

  /// <summary>
  /// 拒绝公会邀请响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RejectGuildInviteResp)]
  public partial class RejectGuildInviteResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 踢出公会成员响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.KickGuildMemberResp)]
  public partial class KickGuildMemberResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 变更公会成员角色响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ChangeGuildMemberRoleResp)]
  public partial class ChangeGuildMemberRoleResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 离开公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.LeaveGuildResp)]
  public partial class LeaveGuildResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 创建公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateGuildAllianceResp)]
  public partial class CreateGuildAllianceResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 接受公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AcceptGuildAllianceResp)]
  public partial class AcceptGuildAllianceResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 拒绝公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RejectGuildAllianceResp)]
  public partial class RejectGuildAllianceResp : MaoYouOutMessage, ILocationResponse
  {
  }

  /// <summary>
  /// 解除公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RemoveGuildAllianceResp)]
  public partial class RemoveGuildAllianceResp : MaoYouOutMessage, ILocationResponse
  {
  }
}
