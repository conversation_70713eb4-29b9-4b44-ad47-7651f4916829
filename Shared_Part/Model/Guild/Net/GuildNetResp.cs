using MemoryPack;

namespace <PERSON>YouJi
{
  /// <summary>
  /// 创建公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateGuildResp)]
  public partial class CreateGuildResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
    public GuildDaoInfo guildDaoInfo { get; set; } // 创建的公会信息
  }

  /// <summary>
  /// 查看我的公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowMyGuildResp)]
  public partial class ShowMyGuildResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
    public GuildDaoInfo guildDaoInfo { get; set; } // 公会信息
  }

  /// <summary>
  /// 编辑公会信息响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.EditGuildResp)]
  public partial class EditGuildResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 解散公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.DisbandGuildResp)]
  public partial class DisbandGuildResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 转让会长响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.TransferGuildLeaderResp)]
  public partial class TransferGuildLeaderResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 邀请公会成员响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.InviteGuildMemberResp)]
  public partial class InviteGuildMemberResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 接受公会邀请响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AcceptGuildInviteResp)]
  public partial class AcceptGuildInviteResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
    public GuildDaoInfo guildDaoInfo { get; set; } // 加入的公会信息
  }

  /// <summary>
  /// 拒绝公会邀请响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RejectGuildInviteResp)]
  public partial class RejectGuildInviteResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 踢出公会成员响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.KickGuildMemberResp)]
  public partial class KickGuildMemberResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 变更公会成员角色响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ChangeGuildMemberRoleResp)]
  public partial class ChangeGuildMemberRoleResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 离开公会响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.LeaveGuildResp)]
  public partial class LeaveGuildResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 创建公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateGuildAllianceResp)]
  public partial class CreateGuildAllianceResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 接受公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AcceptGuildAllianceResp)]
  public partial class AcceptGuildAllianceResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 拒绝公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RejectGuildAllianceResp)]
  public partial class RejectGuildAllianceResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }

  /// <summary>
  /// 解除公会联盟响应
  /// </summary>
  [MemoryPackable]
  [Message(MaoOuterMessageRange.RemoveGuildAllianceResp)]
  public partial class RemoveGuildAllianceResp : MessageObject, ILocationResponse
  {
    public int RpcId { get; set; }
    public int Error { get; set; }
    public string Message { get; set; }
  }
}
