namespace MaoYouJi
{
  [Invoke]
  public class QuartzSchedulerAddJob : AInvokeHandler<MaoScheduleJobInfo>
  {
    public override void Handle(MaoScheduleJobInfo jobInfo)
    {
      jobInfo.Message.RequestId = IdGenerater.Instance.GenerateRequestId();
      if (jobInfo.FightInfo != null)
      {
        ActorId actorId = UserProcSystem.GetParentActorId(jobInfo.FightInfo);
        if (actorId != default)
        {
          MessageQueue.Instance.Send(actorId, jobInfo.Message);
        }
        else
        {
          ETLog.Error($"空的ActorId: {jobInfo.FightInfo.fightId} {jobInfo.FightInfo.liveType}");
        }
      }
      else
      {
        MessageQueue.Instance.Send(jobInfo.ActorId, jobInfo.Message);
      }
    }
  }
}