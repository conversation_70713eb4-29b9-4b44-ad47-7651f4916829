namespace MaoYouJi
{
  /// <summary>
  /// 创建公会处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class CreateGuildHandler : MessageLocationHandler<MapNode, CreateGuildReq, CreateGuildResp>
  {
    protected override async ETTask Run(MapNode nowMap, CreateGuildReq request, CreateGuildResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user, false, true);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }

      // 检查是否可以创建公会
      LogicRet canCreateRet = GuildProcSys.CheckCanCreateGuild(user);
      if (!canCreateRet.IsSuccess)
      {
        response.SetError(canCreateRet.Message);
        return;
      }

      GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();

      // 验证公会名称
      LogicRet validateNameRet = guildManageComponent.ValidateGuildName(request.guildName);
      if (!validateNameRet.IsSuccess)
      {
        response.SetError(validateNameRet.Message);
        return;
      }

      // 扣除创建费用
      LogicRet deductCostRet = GuildProcSys.DeductCreateGuildCost(user);
      if (!deductCostRet.IsSuccess)
      {
        response.SetError(deductCostRet.Message);
        return;
      }

      // 创建公会
      GuildInfo guildInfo = guildManageComponent.CreateGuild(request.guildName, request.description, user);

      response.guildDaoInfo = guildInfo.ToDaoInfo();
      user.SendChat($"成功创建公会 {guildInfo.name}");

      GuildProcSys.LogGuildOperation(guildInfo, user, "创建公会");

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 查看我的公会处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ShowMyGuildHandler : MessageLocationHandler<MapNode, ShowMyGuildReq, ShowMyGuildResp>
  {
    protected override async ETTask Run(MapNode nowMap, ShowMyGuildReq request, ShowMyGuildResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }

      if (user.userComInfo == null)
      {
        response.guildDaoInfo = null;
        return;
      }

      GuildInfo guildInfo = GlobalInfoCache.Instance.GetGuild(user.userComInfo.commId);
      if (guildInfo == null)
      {
        // 清理用户的无效公会信息
        user.userComInfo = null;
        response.guildDaoInfo = null;
        return;
      }

      // 更新成员信息
      guildInfo.UpdateMemberInfo(user);

      response.guildDaoInfo = guildInfo.ToDaoInfo();

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 编辑公会信息处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class EditGuildHandler : MessageLocationHandler<MapNode, EditGuildReq, EditGuildResp>
  {
    protected override async ETTask Run(MapNode nowMap, EditGuildReq request, EditGuildResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查编辑权限
      LogicRet permissionRet = GuildPermissionSystem.CheckEditGuildPermission(guildInfo, user.Id);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 如果要修改公会名称，需要验证新名称
      if (!string.IsNullOrEmpty(request.guildName) && request.guildName != guildInfo.name)
      {
        GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();
        LogicRet validateNameRet = guildManageComponent.ValidateGuildName(request.guildName);
        if (!validateNameRet.IsSuccess)
        {
          response.SetError(validateNameRet.Message);
          return;
        }
      }

      // 编辑公会信息
      LogicRet editRet = guildInfo.EditGuildInfo(request.guildName, request.description, request.announcement);
      if (!editRet.IsSuccess)
      {
        response.SetError(editRet.Message);
        return;
      }

      user.SendChat("公会信息编辑成功");
      GuildProcSys.LogGuildOperation(guildInfo, user, "编辑公会信息");

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 解散公会处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class DisbandGuildHandler : MessageLocationHandler<MapNode, DisbandGuildReq, DisbandGuildResp>
  {
    protected override async ETTask Run(MapNode nowMap, DisbandGuildReq request, DisbandGuildResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user, true);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查解散权限
      LogicRet permissionRet = GuildPermissionSystem.CheckDisbandPermission(guildInfo, user.Id);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 检查是否可以解散
      LogicRet canDisbandRet = GuildProcSys.CheckCanDisbandGuild(guildInfo);
      if (!canDisbandRet.IsSuccess)
      {
        response.SetError(canDisbandRet.Message);
        return;
      }

      string guildName = guildInfo.name;
      GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();
      guildManageComponent.DisbandGuild(guildInfo);

      user.SendChat($"公会 {guildName} 已解散");
      GuildProcSys.LogGuildOperation(null, user, "解散公会", guildName);

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 转让会长处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class TransferGuildLeaderHandler : MessageLocationHandler<MapNode, TransferGuildLeaderReq, TransferGuildLeaderResp>
  {
    protected override async ETTask Run(MapNode nowMap, TransferGuildLeaderReq request, TransferGuildLeaderResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user, true);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查转让权限
      LogicRet permissionRet = GuildPermissionSystem.CheckTransferLeaderPermission(guildInfo, user.Id, request.newLeaderId);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 执行转让
      LogicRet transferRet = guildInfo.TransferLeadership(request.newLeaderId);
      if (!transferRet.IsSuccess)
      {
        response.SetError(transferRet.Message);
        return;
      }

      User newLeader = GlobalInfoCache.Instance.GetOnlineUser(request.newLeaderId);
      user.SendChat("会长职位转让成功");
      GuildProcSys.LogGuildOperation(guildInfo, user, "转让会长", $"新会长: {newLeader?.nickname ?? "未知"}");

      await ETTask.CompletedTask;
    }
  }
}
