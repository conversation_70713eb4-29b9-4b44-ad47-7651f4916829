namespace MaoYouJi
{
  /// <summary>
  /// 邀请公会成员处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class InviteGuildMemberHandler : MessageLocationHandler<MapNode, InviteGuildMemberReq, InviteGuildMemberResp>
  {
    protected override async ETTask Run(MapNode nowMap, InviteGuildMemberReq request, InviteGuildMemberResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查邀请权限
      LogicRet permissionRet = GuildPermissionSystem.CheckInvitePermission(guildInfo, user.Id);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 检查公会是否已满
      if (guildInfo.IsMaxMembers())
      {
        response.SetError("公会成员已满");
        return;
      }

      // 查找目标用户
      LogicRet findUserRet = GuildProcSys.FindUserByIdOrName(request.targetUserId, request.targetUserName, out User targetUser);
      if (!findUserRet.IsSuccess)
      {
        response.SetError(findUserRet.Message);
        return;
      }

      // 检查目标用户是否可以加入公会
      LogicRet canJoinRet = GuildProcSys.CheckCanJoinGuild(targetUser);
      if (!canJoinRet.IsSuccess)
      {
        response.SetError(canJoinRet.Message);
        return;
      }

      // 检查是否已是公会成员
      if (guildInfo.IsMember(targetUser.Id))
      {
        response.SetError("该用户已是公会成员");
        return;
      }

      // 验证邀请消息
      LogicRet validateMsgRet = GuildProcSys.ValidateInviteMessage(request.inviteMessage);
      if (!validateMsgRet.IsSuccess)
      {
        response.SetError(validateMsgRet.Message);
        return;
      }

      // 添加邀请信息
      GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();
      guildManageComponent.AddGuildInviteInfo(guildInfo, user, targetUser, request.inviteMessage);

      user.SendChat($"已向 {targetUser.nickname} 发送公会邀请");
      GuildProcSys.LogGuildOperation(guildInfo, user, "邀请成员", $"目标: {targetUser.nickname}");
      
      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 接受公会邀请处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class AcceptGuildInviteHandler : MessageLocationHandler<MapNode, AcceptGuildInviteReq, AcceptGuildInviteResp>
  {
    protected override async ETTask Run(MapNode nowMap, AcceptGuildInviteReq request, AcceptGuildInviteResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }

      // 检查是否可以加入公会
      LogicRet canJoinRet = GuildProcSys.CheckCanJoinGuild(user);
      if (!canJoinRet.IsSuccess)
      {
        response.SetError(canJoinRet.Message);
        return;
      }

      GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();
      
      // 处理邀请响应
      bool processResult = await guildManageComponent.ProcessGuildInviteResponse(request.inviteId, true, user);
      if (!processResult)
      {
        response.SetError("邀请不存在或已过期");
        return;
      }

      // 获取邀请信息以找到公会
      var collection = nowMap.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<GuildInviteInfo>();
      var inviteInfo = await collection.Find(x => x.id == request.inviteId).FirstOrDefaultAsync();
      if (inviteInfo == null)
      {
        response.SetError("邀请信息不存在");
        return;
      }

      GuildInfo guildInfo = GlobalInfoCache.Instance.GetGuild(inviteInfo.guildId);
      if (guildInfo == null)
      {
        response.SetError("公会不存在");
        return;
      }

      // 再次检查公会是否已满
      if (guildInfo.IsMaxMembers())
      {
        response.SetError("公会成员已满");
        return;
      }

      // 添加成员
      LogicRet addMemberRet = guildInfo.AddMember(user);
      if (!addMemberRet.IsSuccess)
      {
        response.SetError(addMemberRet.Message);
        return;
      }

      response.guildDaoInfo = guildInfo.ToDaoInfo();
      GuildProcSys.LogGuildOperation(guildInfo, user, "接受邀请加入公会");
      
      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 拒绝公会邀请处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class RejectGuildInviteHandler : MessageLocationHandler<MapNode, RejectGuildInviteReq, RejectGuildInviteResp>
  {
    protected override async ETTask Run(MapNode nowMap, RejectGuildInviteReq request, RejectGuildInviteResp response)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!getUserRet.IsSuccess)
      {
        response.SetError(getUserRet.Message);
        return;
      }

      GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();
      
      // 处理邀请响应
      bool processResult = await guildManageComponent.ProcessGuildInviteResponse(request.inviteId, false, user);
      if (!processResult)
      {
        response.SetError("邀请不存在或已过期");
        return;
      }

      user.SendChat("已拒绝公会邀请");
      GuildProcSys.LogGuildOperation(null, user, "拒绝公会邀请");
      
      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 踢出公会成员处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class KickGuildMemberHandler : MessageLocationHandler<MapNode, KickGuildMemberReq, KickGuildMemberResp>
  {
    protected override async ETTask Run(MapNode nowMap, KickGuildMemberReq request, KickGuildMemberResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查踢出权限
      LogicRet permissionRet = GuildPermissionSystem.CheckKickPermission(guildInfo, user.Id, request.targetUserId);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 获取目标用户信息
      if (!guildInfo.memberInfos.TryGetValue(request.targetUserId, out GuildMemberInfo targetMemberInfo))
      {
        response.SetError("目标用户不是公会成员");
        return;
      }

      // 移除成员
      LogicRet removeMemberRet = guildInfo.RemoveMember(request.targetUserId, GuildOperationType.Kicked, request.reason);
      if (!removeMemberRet.IsSuccess)
      {
        response.SetError(removeMemberRet.Message);
        return;
      }

      user.SendChat($"已将 {targetMemberInfo.name} 踢出公会");
      
      // 通知被踢出的用户
      User targetUser = GlobalInfoCache.Instance.GetOnlineUser(request.targetUserId);
      targetUser?.SendChat($"您已被踢出公会 {guildInfo.name}" + 
        (string.IsNullOrEmpty(request.reason) ? "" : $"，原因：{request.reason}"));

      GuildProcSys.LogGuildOperation(guildInfo, user, "踢出成员", $"目标: {targetMemberInfo.name}, 原因: {request.reason}");
      
      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 变更公会成员角色处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ChangeGuildMemberRoleHandler : MessageLocationHandler<MapNode, ChangeGuildMemberRoleReq, ChangeGuildMemberRoleResp>
  {
    protected override async ETTask Run(MapNode nowMap, ChangeGuildMemberRoleReq request, ChangeGuildMemberRoleResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查变更角色权限
      LogicRet permissionRet = GuildPermissionSystem.CheckChangeRolePermission(guildInfo, user.Id, request.targetUserId, request.newRole);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 获取目标用户信息
      if (!guildInfo.memberInfos.TryGetValue(request.targetUserId, out GuildMemberInfo targetMemberInfo))
      {
        response.SetError("目标用户不是公会成员");
        return;
      }

      GuildRole oldRole = targetMemberInfo.role;

      // 变更角色
      LogicRet changeRoleRet = guildInfo.ChangeMemberRole(request.targetUserId, request.newRole);
      if (!changeRoleRet.IsSuccess)
      {
        response.SetError(changeRoleRet.Message);
        return;
      }

      user.SendChat($"已将 {targetMemberInfo.name} 的角色从 {GuildProcSys.GetGuildRoleDescription(oldRole)} 变更为 {GuildProcSys.GetGuildRoleDescription(request.newRole)}");
      
      GuildProcSys.LogGuildOperation(guildInfo, user, "变更成员角色", 
        $"目标: {targetMemberInfo.name}, {GuildProcSys.GetGuildRoleDescription(oldRole)} -> {GuildProcSys.GetGuildRoleDescription(request.newRole)}");
      
      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 离开公会处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class LeaveGuildHandler : MessageLocationHandler<MapNode, LeaveGuildReq, LeaveGuildResp>
  {
    protected override async ETTask Run(MapNode nowMap, LeaveGuildReq request, LeaveGuildResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 会长不能直接离开公会，需要先转让会长职位或解散公会
      if (guildInfo.leaderId == user.Id)
      {
        response.SetError("会长不能直接离开公会，请先转让会长职位或解散公会");
        return;
      }

      // 移除成员
      LogicRet removeMemberRet = guildInfo.RemoveMember(user.Id, GuildOperationType.Leave);
      if (!removeMemberRet.IsSuccess)
      {
        response.SetError(removeMemberRet.Message);
        return;
      }

      user.SendChat($"您已离开公会 {guildInfo.name}");
      GuildProcSys.LogGuildOperation(guildInfo, user, "离开公会");
      
      await ETTask.CompletedTask;
    }
  }
}
