namespace MaoYouJi
{
  /// <summary>
  /// 公会处理工具类
  /// </summary>
  public static class GuildProcSys
  {
    /// <summary>
    /// 获取用户公会信息并进行检查
    /// </summary>
    public static LogicRet GetUserGuildWithCheck(long userId, out GuildInfo guildInfo, out User user, bool requireLeader = false)
    {
      guildInfo = null;
      user = GlobalInfoCache.Instance.GetOnlineUser(userId);

      if (user == null)
      {
        return LogicRet.Failed("用户不在线");
      }

      if (user.userComInfo == null)
      {
        return LogicRet.Failed("您还没有加入公会");
      }

      guildInfo = GlobalInfoCache.Instance.GetGuild(user.userComInfo.commId);
      if (guildInfo == null)
      {
        // 清理用户的无效公会信息
        user.userComInfo = null;
        return LogicRet.Failed("公会不存在");
      }

      if (requireLeader && guildInfo.leaderId != userId)
      {
        return LogicRet.Failed("只有会长可以执行此操作");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查用户是否可以创建公会
    /// </summary>
    public static LogicRet CheckCanCreateGuild(User user)
    {
      if (user.userComInfo != null)
      {
        return LogicRet.Failed("您已加入公会，无法创建新公会");
      }

      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("大逃杀活动中不能创建公会");
      }

      // 检查是否有足够的金币
      BagComponent bag = user.GetComponent<BagComponent>();
      if (bag.coin < GuildManageComponent.CREATE_GUILD_COST_COIN)
      {
        return LogicRet.Failed($"创建公会需要{GuildManageComponent.CREATE_GUILD_COST_COIN}金币");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查用户是否可以加入公会
    /// </summary>
    public static LogicRet CheckCanJoinGuild(User user)
    {
      if (user.userComInfo != null)
      {
        return LogicRet.Failed("您已加入公会");
      }

      if (user.activityName == ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("大逃杀活动中不能加入公会");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 根据用户ID或昵称查找用户
    /// </summary>
    public static LogicRet FindUserByIdOrName(long? targetUserId, string targetUserName, out User targetUser)
    {
      targetUser = null;

      if (targetUserId.HasValue && targetUserId.Value > 0)
      {
        targetUser = GlobalInfoCache.Instance.GetOnlineUser(targetUserId.Value);
        if (targetUser == null)
        {
          return LogicRet.Failed("目标用户不在线");
        }
      }
      else if (!string.IsNullOrEmpty(targetUserName))
      {
        // 通过昵称查找在线用户
        foreach (var userKvp in GlobalInfoCache.Instance.allOnlineUserCache)
        {
          if (userKvp.Value.nickname.Equals(targetUserName, System.StringComparison.OrdinalIgnoreCase))
          {
            targetUser = userKvp.Value;
            break;
          }
        }

        if (targetUser == null)
        {
          return LogicRet.Failed("未找到该昵称的在线用户");
        }
      }
      else
      {
        return LogicRet.Failed("请提供目标用户ID或昵称");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 根据公会ID或名称查找公会
    /// </summary>
    public static LogicRet FindGuildByIdOrName(long? targetGuildId, string targetGuildName, out GuildInfo targetGuild)
    {
      targetGuild = null;

      if (targetGuildId.HasValue && targetGuildId.Value > 0)
      {
        targetGuild = GlobalInfoCache.Instance.GetGuild(targetGuildId.Value);
        if (targetGuild == null)
        {
          return LogicRet.Failed("目标公会不存在");
        }
      }
      else if (!string.IsNullOrEmpty(targetGuildName))
      {
        targetGuild = GlobalInfoCache.Instance.GetGuildByName(targetGuildName);
        if (targetGuild == null)
        {
          return LogicRet.Failed("未找到该名称的公会");
        }
      }
      else
      {
        return LogicRet.Failed("请提供目标公会ID或名称");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 检查用户是否在战斗状态
    /// </summary>
    public static LogicRet CheckUserNotInBattle(User user)
    {
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.ALIVE)
      {
        return LogicRet.Failed("战斗状态下无法执行此操作");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 扣除创建公会费用
    /// </summary>
    public static LogicRet DeductCreateGuildCost(User user)
    {
      BagComponent bag = user.GetComponent<BagComponent>();
      if (bag.coin < GuildManageComponent.CREATE_GUILD_COST_COIN)
      {
        return LogicRet.Failed($"金币不足，创建公会需要{GuildManageComponent.CREATE_GUILD_COST_COIN}金币");
      }

      bag.AddAllCoinWithSend(-GuildManageComponent.CREATE_GUILD_COST_COIN);
      user.SendChat($"扣除{GuildManageComponent.CREATE_GUILD_COST_COIN}金币创建公会费用");

      return LogicRet.Success;
    }

    /// <summary>
    /// 填充公会成员信息
    /// </summary>
    public static void FillGuildMemberInfo(GuildMemberInfo memberInfo, User user)
    {
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      memberInfo.userId = user.Id;
      memberInfo.name = user.nickname;
      memberInfo.level = (int)attackComponent.level;
      memberInfo.attackNum = attackComponent.attackNum;
      memberInfo.offlineState = user.offlineState;
      memberInfo.lastActiveTime = TimeInfo.Instance.ServerNow();
    }

    /// <summary>
    /// 验证邀请消息内容
    /// </summary>
    public static LogicRet ValidateInviteMessage(string message)
    {
      if (string.IsNullOrEmpty(message))
      {
        return LogicRet.Success; // 允许空消息
      }

      if (message.Length > 200)
      {
        return LogicRet.Failed("邀请消息长度不能超过200个字符");
      }

      // 可以添加更多的内容检查，如敏感词过滤等
      return LogicRet.Success;
    }

    /// <summary>
    /// 检查公会是否可以解散
    /// </summary>
    public static LogicRet CheckCanDisbandGuild(GuildInfo guildInfo)
    {
      // 检查是否有联盟关系
      if (guildInfo.allianceInfos.Count > 0)
      {
        return LogicRet.Failed("请先解除所有联盟关系再解散公会");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 获取公会角色的中文描述
    /// </summary>
    public static string GetGuildRoleDescription(GuildRole role)
    {
      return role switch
      {
        GuildRole.Member => "会员",
        GuildRole.Manager => "管理员",
        GuildRole.VicePresident => "副会长",
        GuildRole.President => "会长",
        _ => "未知"
      };
    }

    /// <summary>
    /// 检查公会操作冷却时间
    /// </summary>
    public static LogicRet CheckGuildOperationCooldown(User user, string operationType)
    {
      // 这里可以实现操作冷却检查
      // 例如：离开公会后24小时内不能加入新公会等
      return LogicRet.Success;
    }

    /// <summary>
    /// 记录公会操作日志
    /// </summary>
    public static void LogGuildOperation(GuildInfo guildInfo, User operator_, string operation, string details = "")
    {
      string logMessage = $"公会操作: 公会={guildInfo?.name ?? "未知"}, 操作者={operator_?.nickname ?? "系统"}, 操作={operation}";
      if (!string.IsNullOrEmpty(details))
      {
        logMessage += $", 详情={details}";
      }
      ETLog.Info(logMessage);
    }
  }
}
