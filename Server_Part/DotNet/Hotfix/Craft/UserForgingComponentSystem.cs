namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserForgingComponent))]
  [FriendOf(typeof(UserForgingComponent))]
  public static partial class UserForgingComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserForgingComponent self)
    {
    }

    // 学习锻造配方
    public static bool LearnForgingRecipe(this UserForgingComponent self, ForgingRecipeIdEnum recipeId)
    {
      if (self.learnedRecipes.Contains(recipeId))
      {
        return false; // 已经学会了
      }
      self.learnedRecipes.Add(recipeId);
      return true;
    }

    // 检查是否已学会锻造配方
    public static bool HasForgingRecipe(this UserForgingComponent self, ForgingRecipeIdEnum recipeId)
    {
      return self.learnedRecipes.Contains(recipeId);
    }

    // 增加锻造系统熟练度
    public static void AddForgingExp(this UserForgingComponent self, int exp)
    {
      User user = self.GetParent<User>();
      if (user == null) return;

      // 获取技能组件
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();

      // 通过技能系统增加锻造技能经验
      skillComponent.AddLifeSkillExp(SkillIdEnum.Forging_Skill, exp);
    }

    // 获取锻造技能等级
    public static int GetForgingLevel(this UserForgingComponent self)
    {
      User user = self.GetParent<User>();
      if (user == null) return 0;

      // 获取技能组件
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();

      // 从技能系统获取锻造技能等级
      Skill forgingSkill = skillComponent.GetSkill(SkillIdEnum.Forging_Skill);
      return forgingSkill?.level ?? 0;
    }

    // 获取已学会的锻造配方数量
    public static int GetLearnedRecipeCount(this UserForgingComponent self)
    {
      return self.learnedRecipes.Count;
    }
  }

}