namespace MaoYouJi
{

  [EntitySystemOf(typeof(UserCraftingComponent))]
  [FriendOf(typeof(UserCraftingComponent))]
  public static partial class UserCraftingComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserCraftingComponent self)
    {
    }

    // 学习合成配方
    public static bool LearnCraftingRecipe(this UserCraftingComponent self, CraftingRecipeIdEnum recipeId)
    {
      if (self.learnedRecipes.Contains(recipeId))
      {
        return false; // 已经学会了
      }
      self.learnedRecipes.Add(recipeId);
      return true;
    }

    // 检查是否已学会合成配方
    public static bool HasCraftingRecipe(this UserCraftingComponent self, CraftingRecipeIdEnum recipeId)
    {
      return self.learnedRecipes.Contains(recipeId);
    }

    // 增加合成系统熟练度
    public static void AddCraftingExp(this UserCraftingComponent self, int exp)
    {
      User user = self.GetParent<User>();
      if (user == null) return;

      // 获取技能组件
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();

      // 通过技能系统增加合成技能经验
      skillComponent.AddLifeSkillExp(SkillIdEnum.Crafting_Skill, exp);
    }

    // 获取合成技能等级
    public static int GetCraftingLevel(this UserCraftingComponent self)
    {
      User user = self.GetParent<User>();
      if (user == null) return 0;

      // 获取技能组件
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();

      // 从技能系统获取合成技能等级
      Skill craftingSkill = skillComponent.GetSkill(SkillIdEnum.Crafting_Skill);
      return craftingSkill?.level ?? 0;
    }

    // 获取已学会的合成配方数量
    public static int GetLearnedRecipeCount(this UserCraftingComponent self)
    {
      return self.learnedRecipes.Count;
    }
  }
}