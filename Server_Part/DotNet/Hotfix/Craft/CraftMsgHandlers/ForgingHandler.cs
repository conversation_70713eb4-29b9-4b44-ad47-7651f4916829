namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ForgingHandler : MessageLocationHandler<MapNode, ForgingReq, ForgingResp>
  {
    protected override async ETTask Run(MapNode nowMap, ForgingReq request, ForgingResp response)
    {
      // 获取用户并验证
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }

      // 检查用户是否学会了该锻造配方
      UserForgingComponent forgingComponent = user.GetComponent<UserForgingComponent>();
      if (forgingComponent == null || !forgingComponent.HasForgingRecipe(request.recipeId))
      {
        response.SetError("您还没有学会这个锻造配方！");
        return;
      }

      // 获取锻造配方
      if (!GlobalInfoCache.Instance.allForgingRecipeCache.TryGetValue(request.recipeId, out ForgingRecipe recipe))
      {
        response.SetError("锻造配方不存在");
        return;
      }

      // 检查锻造等级是否足够
      int currentForgingLevel = forgingComponent.GetForgingLevel();
      if (currentForgingLevel < recipe.needForginLevel)
      {
        response.SetError($"锻造等级不足！需要等级：{recipe.needForginLevel}，当前等级：{currentForgingLevel}");
        return;
      }

      // 获取用户背包
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      // 检查材料是否足够
      LogicRet materialCheckRet = bagComponent.CheckMaterials(recipe.needThings);
      if (!materialCheckRet.IsSuccess)
      {
        response.SetError(materialCheckRet.Message);
        return;
      }
      // 扣除材料
      LogicRet consumeRet = bagComponent.ConsumeMaterials(recipe.needThings);
      if (!consumeRet.IsSuccess)
      {
        response.SetError(consumeRet.Message);
        return;
      }
      // 给予产出物品
      if (recipe.giveThings != null && recipe.giveThings.Count > 0)
      {
        bagComponent.GiveThing(recipe.giveThings, ThingFromType.Forging);
      }

      // 增加熟练度
      forgingComponent.AddForgingExp(recipe.expReward);

      user.SendToast($"成功锻造了{recipe.name}！");
      await ETTask.CompletedTask;
    }
  }
}
