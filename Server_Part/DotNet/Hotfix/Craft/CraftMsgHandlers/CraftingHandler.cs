namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class CraftingHandler : MessageLocationHandler<MapNode, CraftingReq, CraftingResp>
  {
    protected override async ETTask Run(MapNode nowMap, CraftingReq request, CraftingResp response)
    {
      // 获取用户并验证
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }

      // 检查用户是否学会了该合成配方
      UserCraftingComponent craftingComponent = user.GetComponent<UserCraftingComponent>();
      if (craftingComponent == null || !craftingComponent.HasCraftingRecipe(request.recipeId))
      {
        response.SetError("您还没有学会这个合成配方！");
        return;
      }

      // 获取合成配方
      if (!GlobalInfoCache.Instance.allCraftingRecipeCache.TryGetValue(request.recipeId, out CraftingRecipe recipe))
      {
        response.SetError("合成配方不存在");
        return;
      }

      // 检查合成等级是否足够
      int currentCraftingLevel = craftingComponent.GetCraftingLevel();
      if (currentCraftingLevel < recipe.needCraftingLevel)
      {
        response.SetError($"合成等级不足！需要等级：{recipe.needCraftingLevel}，当前等级：{currentCraftingLevel}");
        return;
      }

      // 获取用户背包
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      // 检查材料是否足够
      LogicRet materialCheckRet = bagComponent.CheckMaterials(recipe.needThings);
      if (!materialCheckRet.IsSuccess)
      {
        response.SetError(materialCheckRet.Message);
        return;
      }
      // 扣除材料
      LogicRet consumeRet = bagComponent.ConsumeMaterials(recipe.needThings);
      if (!consumeRet.IsSuccess)
      {
        response.SetError(consumeRet.Message);
        return;
      }
      // 给予产出物品
      if (recipe.giveThings != null && recipe.giveThings.Count > 0)
      {
        bagComponent.GiveThing(recipe.giveThings, ThingFromType.Crafting);
      }

      // 增加熟练度
      craftingComponent.AddCraftingExp(recipe.expReward);

      user.SendToast($"成功合成了{recipe.name}！");
      await ETTask.CompletedTask;
    }
  }
}
