using System;
using System.Collections.Generic;
using System.Linq;
using ConcurrentCollections;

namespace MaoYouJi
{
  [EntitySystem]
  [FriendOf(typeof(BagComponent))]
  public static partial class BagRemoveSystem
  {
    /**
    * 移除一个物品
    *
    * @param thingId 物品id
    */
    public static void RemoveOneThing(this BagComponent bag, long thingId)
    {
      if (!bag.thingMap.TryGetValue(thingId, out Thing thing))
      {
        return;
      }
      bag.RemoveThing(thing);
    }

    public static void RemoveThings(this BagComponent bag, List<long> thingIds)
    {
      foreach (long thingId in thingIds)
      {
        bag.RemoveOneThing(thingId);
      }
    }

    // 移除物品
    public static void RemoveThing(this BagComponent bag, Thing thing)
    {
      User user = bag.GetParent<User>();
      bag.thingMap.TryRemove(thing.thingId, out _);
      bag.bagIdLock.EnterWriteLock();
      bag.thingIds.Remove(thing.thingId);
      bag.bagIdLock.ExitWriteLock();
      bag.nameIdMap.TryGetValue(thing.thingName, out ConcurrentHashSet<long> thingIds);
      thingIds.TryRemove(thing.thingId);
      if (thingIds.Count == 0)
      {
        bag.nameIdMap.TryRemove(thing.thingName, out _);
      }
      if (user != null)
      {
        AttackComponent attackComponent = user.GetComponent<AttackComponent>();
        SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();
        if (skillComponent.RemoveQuickFood(thing.thingId))
        {
          user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Quick_Bar));
        }
      }
    }

    /**
    * 移除物品列表
    *
    * @param thingNames 物品名称列表
    */
    public static void RemoveThingsWithSend(this BagComponent bag, List<ThingNameEnum> thingNames)
    {
      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagThingsOut = new ServerUpdateBagMsg();
      foreach (ThingNameEnum thingName in thingNames)
      {
        bag.nameIdMap.TryGetValue(thingName, out ConcurrentHashSet<long> thingIds);
        if (thingIds == null)
        {
          continue;
        }
        foreach (long thingId in thingIds)
        {
          bag.thingMap.TryGetValue(thingId, out Thing thing);
          if (thing == null)
          {
            continue;
          }
          bag.RemoveThing(thing);
          user?.SendChat("背包中的" + thing.name + "被移除了");
          updateBagThingsOut.removeThingIds.Add(thingId);
        }
      }
      if (updateBagThingsOut.removeThingIds.Count == 0 && updateBagThingsOut.updateList.Count == 0)
      {
        return;
      }
      user?.SendMessage(updateBagThingsOut);
    }

    public static void RemoveGiveThingWithSend(this BagComponent bag, List<ThingGiveInfo> thingGiveInfos)
    {
      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagThingsOut = new();
      foreach (ThingGiveInfo thingGiveInfo in thingGiveInfos)
      {
        bag.nameIdMap.TryGetValue(thingGiveInfo.thingName, out ConcurrentHashSet<long> thingIds);
        List<Thing> things = new();
        if (thingIds == null)
        {
          continue;
        }
        foreach (long thingId in thingIds)
        {
          bag.thingMap.TryGetValue(thingId, out Thing thing);
          if (thing == null)
          {
            continue;
          }
          if (thingGiveInfo.ownType != OwnType.None && thing.ownType != thingGiveInfo.ownType)
          {
            continue;
          }
          things.Add(thing);
        }
        foreach (Thing thing in things)
        {
          if (thingGiveInfo.num <= 0)
          {
            break;
          }
          if (thing.num > thingGiveInfo.num)
          {
            user?.SendChat("背包中的" + thing.name + "数量减少" + thingGiveInfo.num);
            thing.num -= thingGiveInfo.num;
            thingGiveInfo.num = 0;
            updateBagThingsOut.updateList.Add(thing);
          }
          else
          {
            user?.SendChat("背包中的" + thing.name + "数量减少" + thing.num);
            bag.RemoveThing(thing);
            thingGiveInfo.num -= thing.num;
            thing.num = 0;
            updateBagThingsOut.removeThingIds.Add(thing.thingId);
          }
        }
      }
      if (updateBagThingsOut.removeThingIds.Count == 0 && updateBagThingsOut.updateList.Count == 0)
      {
        return;
      }
      user?.SendMessage(updateBagThingsOut);
    }

    /// <summary>
    /// 扣除材料并发送背包更新消息
    /// </summary>
    /// <param name="bag">背包组件</param>
    /// <param name="needThings">需要扣除的材料列表</param>
    /// <returns>扣除结果</returns>
    public static LogicRet ConsumeMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)
    {
      if (needThings == null || needThings.Count == 0)
      {
        return LogicRet.Success;
      }

      User user = bag.GetParent<User>();
      ServerUpdateBagMsg updateBagMsg = new ServerUpdateBagMsg();
      List<Thing> thingsToRemove = new List<Thing>();
      List<string> chatMessages = new List<string>(); // 存储聊天消息

      try
      {
        foreach (ThingGiveInfo needThing in needThings)
        {
          // 获取背包中的物品
          List<Thing> things = bag.GetThingInBag(needThing.thingName, needThing.ownType);
          if (things == null || things.Count == 0)
          {
            return LogicRet.Failed($"背包中没有找到材料 {needThing.thingName}");
          }

          int remainingNeed = needThing.num;
          int totalConsumed = 0; // 记录总共消耗的数量

          // 按数量扣除物品
          foreach (Thing thing in things)
          {
            if (remainingNeed <= 0) break;

            int consumedFromThisThing = 0; // 记录从这个物品中消耗的数量

            if (thing.num >= remainingNeed)
            {
              consumedFromThisThing = remainingNeed;
              thing.num -= remainingNeed;
              totalConsumed += remainingNeed;
              remainingNeed = 0;

              if (thing.num <= 0)
              {
                thingsToRemove.Add(thing);
                updateBagMsg.removeThingIds.Add(thing.thingId);
                // 物品被完全移除
                chatMessages.Add($"{thing.name} 已移除");
              }
              else
              {
                updateBagMsg.updateList.Add(thing);
                // 物品数量减少但未移除
                chatMessages.Add($"{thing.name} -{consumedFromThisThing}");
              }
            }
            else
            {
              consumedFromThisThing = thing.num;
              remainingNeed -= thing.num;
              totalConsumed += thing.num;
              thing.num = 0;
              thingsToRemove.Add(thing);
              updateBagMsg.removeThingIds.Add(thing.thingId);
              // 物品被完全移除
              chatMessages.Add($"{thing.name} 已移除");
            }
          }

          if (remainingNeed > 0)
          {
            return LogicRet.Failed($"材料 {needThing.thingName} 数量不足");
          }
        }

        // 移除数量为0的物品
        foreach (Thing thing in thingsToRemove)
        {
          bag.RemoveThing(thing);
          ETLog.Info($"ConsumeMaterials remove thing: {bag.ownId}, {thing.thingName}, {thing.ownType}, {thing.grade}");
        }

        // 发送背包更新消息
        if (updateBagMsg.removeThingIds.Count > 0 || updateBagMsg.updateList.Count > 0)
        {
          user?.SendMessage(updateBagMsg);
        }

        // 发送聊天消息通知物品变化
        if (chatMessages.Count > 0 && user != null)
        {
          // 将消息分组，避免单条消息过长
          const int maxItemsPerMessage = 5; // 每条消息最多显示5个物品变化
          for (int i = 0; i < chatMessages.Count; i += maxItemsPerMessage)
          {
            var messageGroup = chatMessages.Skip(i).Take(maxItemsPerMessage);
            string content = "背包物品变化：" + string.Join("，", messageGroup);
            user.SendChat(content);
          }
        }

        return LogicRet.Success;
      }
      catch (Exception ex)
      {
        ETLog.Error($"扣除材料时发生错误: {ex.Message}");
        return LogicRet.Failed("扣除材料时发生内部错误");
      }
    }
  }
}
