# 物品系统快速参考 (For AI)

## 检查功能
```csharp
// 容量检查
bool HasEnoughCapacity(this BagComponent bag, int num)
int NeedRemainCapacity(this BagComponent bag, List<Thing> things)

// 材料检查  
LogicRet CheckMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)
```

## 给予功能
```csharp
// 底层添加
void AddThing(this BagComponent bag, Thing thing)
void AddThingNumWithSend(this BagComponent bag, Thing thing, int num)

// 货币处理
void AddAllCoinWithSend(this BagComponent bag, long coinNum, long catBeanNum = 0, long catEyeNum = 0)

// 高级给予接口
Thing GiveThing(this BagComponent bag, ThingGiveInfo giveInfo, ThingFromType fromType = ThingFromType.None)
List<Thing> GiveThing(this BagComponent bag, List<ThingGiveInfo> giveInfos, ThingFromType fromType = ThingFromType.None)
void GiveThingList(this BagComponent bag, List<Thing> things)
```

## 移除功能
```csharp
// 基础移除
void RemoveOneThing(this BagComponent bag, long thingId)
void RemoveThings(this BagComponent bag, List<long> thingIds)
void RemoveThing(this BagComponent bag, Thing thing)

// 高级移除
void RemoveThingsWithSend(this BagComponent bag, List<ThingNameEnum> thingNames)
void RemoveGiveThingWithSend(this BagComponent bag, List<ThingGiveInfo> thingGiveInfos)

// 材料消耗(推荐)
LogicRet ConsumeMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)
```

## 核心数据结构
- `BagComponent`: 背包组件，所有操作的目标
- `Thing`: 物品实体
- `ThingGiveInfo`: 物品给予信息(含名称、数量、所有权类型)
- `ThingNameEnum`: 物品名称枚举
- `LogicRet`: 逻辑操作结果(成功/失败+信息)

## 常用操作模式
1. **给予物品**: `GiveThing`
2. **消耗材料**: `CheckMaterials` → `ConsumeMaterials`  
3. **批量处理**: 使用批量接口而非循环调用单个接口 