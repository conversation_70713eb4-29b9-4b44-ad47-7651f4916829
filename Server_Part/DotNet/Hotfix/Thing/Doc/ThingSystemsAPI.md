# 物品系统API接口文档 (For AI)

## 概述
物品系统包含三个核心功能模块：检查(Check)、给予(Give)、移除(Remove)。所有方法都在`MaoYouJi`命名空间下的静态类中。

## 方法类型说明
- **扩展方法**: 第一个参数为`this BagComponent bag`，对背包组件的扩展功能
- **静态方法**: 独立的静态功能方法

---

## 1. 检查功能 (BagCheckSystem)

### 扩展方法

#### HasEnoughCapacity
- **签名**: `public static bool HasEnoughCapacity(this BagComponent bag, int num)`
- **功能**: 检查背包是否有足够容量存放指定数量的物品
- **参数**: 
  - `bag`: 背包组件
  - `num`: 需要存放的物品数量
- **返回**: bool值，true表示容量足够

#### NeedRemainCapacity
- **签名**: `public static int NeedRemainCapacity(this BagComponent bag, List<Thing> things)`
- **功能**: 计算存放指定物品列表需要的剩余容量
- **参数**:
  - `bag`: 背包组件
  - `things`: 要存放的物品列表
- **返回**: int值，需要的剩余容量数量
- **逻辑**: 智能计算，考虑物品堆叠和现有物品的空余堆叠空间

#### CheckMaterials
- **签名**: `public static LogicRet CheckMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)`
- **功能**: 检查背包中是否有足够的材料
- **参数**:
  - `bag`: 背包组件
  - `needThings`: 需要的材料清单
- **返回**: LogicRet结果，包含成功/失败状态和详细信息
- **用途**: 用于合成、升级等需要消耗材料的操作前置检查

---

## 2. 给予功能 (BagGiveSystem)

### 扩展方法

#### AddThing
- **签名**: `public static void AddThing(this BagComponent bag, Thing thing)`
- **功能**: 底层添加物品到背包的核心方法
- **参数**:
  - `bag`: 背包组件
  - `thing`: 要添加的物品实体
- **特性**: 自动处理ID生成、索引更新，不发送通知消息

#### AddThingNumWithSend
- **签名**: `public static void AddThingNumWithSend(this BagComponent bag, Thing thing, int num)`
- **功能**: 修改现有物品数量并发送更新通知
- **参数**:
  - `bag`: 背包组件
  - `thing`: 目标物品
  - `num`: 变化数量(可为负数)
- **特性**: 自动发送背包更新消息和聊天通知

#### SendNewCoin
- **签名**: `public static void SendNewCoin(this BagComponent bag)`
- **功能**: 发送货币信息更新消息
- **参数**: `bag`: 背包组件
- **用途**: 更新客户端显示的金币、猫豆、猫眼等货币数量

#### AddAllCoinWithSend
- **签名**: `public static void AddAllCoinWithSend(this BagComponent bag, long coinNum, long catBeanNum = 0, long catEyeNum = 0)`
- **功能**: 添加各种货币并发送通知
- **参数**:
  - `bag`: 背包组件
  - `coinNum`: 金币数量
  - `catBeanNum`: 猫豆数量(可选)
  - `catEyeNum`: 猫眼数量(可选)
- **特性**: 自动发送货币更新和聊天提示

#### GiveThingList
- **签名**: `public static void GiveThingList(this BagComponent bag, List<Thing> things)`
- **功能**: 批量给予物品列表
- **参数**:
  - `bag`: 背包组件
  - `things`: 物品列表
- **逻辑**: 智能处理物品堆叠、容量检查、自动合并相同物品

#### GiveThing (单个)
- **签名**: `public static Thing GiveThing(this BagComponent bag, ThingGiveInfo giveInfo, ThingFromType fromType = ThingFromType.None)`
- **功能**: 给予单个物品
- **参数**:
  - `bag`: 背包组件
  - `giveInfo`: 物品给予信息
  - `fromType`: 物品来源类型(可选)
- **返回**: 给予的物品实体

#### GiveThing (批量)
- **签名**: `public static List<Thing> GiveThing(this BagComponent bag, List<ThingGiveInfo> giveInfos, ThingFromType fromType = ThingFromType.None)`
- **功能**: 批量给予物品的高级接口
- **参数**:
  - `bag`: 背包组件
  - `giveInfos`: 物品给予信息列表
  - `fromType`: 物品来源类型(可选)
- **返回**: 实际给予的物品列表
- **特性**: 
  - 自动处理猫豆、猫眼等特殊货币
  - 智能堆叠和装备属性生成
  - 容量检查和失败处理
  - 事件发布和通知发送

---

## 3. 移除功能 (BagRemoveSystem)

### 扩展方法

#### RemoveOneThing
- **签名**: `public static void RemoveOneThing(this BagComponent bag, long thingId)`
- **功能**: 根据物品ID移除单个物品
- **参数**:
  - `bag`: 背包组件
  - `thingId`: 物品唯一ID
- **用途**: 精确移除指定物品

#### RemoveThings
- **签名**: `public static void RemoveThings(this BagComponent bag, List<long> thingIds)`
- **功能**: 批量移除指定ID的物品
- **参数**:
  - `bag`: 背包组件
  - `thingIds`: 物品ID列表

#### RemoveThing
- **签名**: `public static void RemoveThing(this BagComponent bag, Thing thing)`
- **功能**: 移除指定物品实体的底层方法
- **参数**:
  - `bag`: 背包组件
  - `thing`: 要移除的物品
- **特性**: 
  - 更新所有相关索引
  - 处理快捷栏物品移除
  - 不发送客户端通知

#### RemoveThingsWithSend
- **签名**: `public static void RemoveThingsWithSend(this BagComponent bag, List<ThingNameEnum> thingNames)`
- **功能**: 根据物品名称移除所有同类物品并发送通知
- **参数**:
  - `bag`: 背包组件
  - `thingNames`: 物品名称列表
- **特性**: 移除指定名称的所有物品，发送更新消息

#### RemoveGiveThingWithSend
- **签名**: `public static void RemoveGiveThingWithSend(this BagComponent bag, List<ThingGiveInfo> thingGiveInfos)`
- **功能**: 按照给予信息格式移除物品并发送通知
- **参数**:
  - `bag`: 背包组件
  - `thingGiveInfos`: 要移除的物品信息列表
- **逻辑**: 
  - 按数量扣除，优先扣除数量多的物品
  - 支持所有权类型过滤
  - 自动处理物品数量减少和完全移除

#### ConsumeMaterials
- **签名**: `public static LogicRet ConsumeMaterials(this BagComponent bag, List<ThingGiveInfo> needThings)`
- **功能**: 高级材料消耗接口，用于合成等功能
- **参数**:
  - `bag`: 背包组件
  - `needThings`: 需要消耗的材料列表
- **返回**: LogicRet结果，包含成功/失败状态
- **特性**:
  - 事务性操作，要么全部成功要么全部失败
  - 详细的错误信息提示
  - 自动发送更新消息和聊天通知
  - 异常处理和日志记录

---

## 使用建议

### 推荐的操作流程:
1. **给予物品前**: 使用`HasEnoughCapacity`或`NeedRemainCapacity`检查容量
2. **消耗材料前**: 使用`CheckMaterials`验证材料充足性
3. **给予物品**: 使用`GiveThing`系列方法
4. **消耗材料**: 使用`ConsumeMaterials`而非直接使用Remove方法

### 性能考虑:
- 批量操作时优先使用批量接口
- 频繁操作时使用不带Send的底层方法，最后统一发送更新
- 避免在循环中重复调用发送通知的方法 