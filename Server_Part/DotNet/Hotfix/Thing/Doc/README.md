# 物品系统文档说明

## 文档目的
这些文档专门为AI阅读设计，旨在帮助AI理解和使用物品系统的接口。

## 文档结构

### ThingSystemsAPI.md
- **完整的API接口文档**
- 包含所有方法的详细说明、参数、返回值和使用特性
- 按功能分类：检查、给予、移除
- 区分扩展方法和静态方法
- 包含使用建议和性能考虑

### QuickReference.md  
- **快速参考手册**
- 仅包含最重要的方法签名
- 核心数据结构说明
- 常用操作模式
- 适合快速查找和确认接口

## 使用建议
1. 初次了解系统时阅读 `ThingSystemsAPI.md`
2. 日常开发时参考 `QuickReference.md`
3. 所有方法都是 `BagComponent` 的扩展方法
4. 遵循先检查后操作的原则
5. 优先使用高级接口而非底层方法

## 系统架构
物品系统基于ECS架构，所有功能都作为 `BagComponent` 的扩展方法实现，确保功能的模块化和可扩展性。 