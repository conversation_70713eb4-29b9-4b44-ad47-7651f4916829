# 技能系统API接口文档 (For AI)

## 概述
技能系统包含两个核心组件系统：技能基础系统(SkillComponentSystem)和技能管理系统(SkillManageComponentSystem)。所有方法都在`MaoYouJi`命名空间下的静态类中。

## 方法类型说明
- **扩展方法**: 第一个参数为`this SkillComponent self`或`this SkillManageComponent self`，对技能组件的扩展功能
- **静态方法**: 独立的静态功能方法

---

## 1. 技能学习与管理功能 (SkillComponentSystem)

### 扩展方法

#### AddLearnSkill (通过枚举)
- **签名**: `public static void AddLearnSkill(this SkillComponent self, SkillIdEnum skillIdEnum)`
- **功能**: 通过技能枚举ID学习技能
- **参数**: 
  - `self`: 技能组件
  - `skillIdEnum`: 技能ID枚举
- **特性**: 自动从全局缓存获取技能信息并学习

#### AddLearnSkill (通过技能对象)
- **签名**: `public static void AddLearnSkill(this SkillComponent self, Skill skill)`
- **功能**: 学习指定的技能对象
- **参数**:
  - `self`: 技能组件
  - `skill`: 要学习的技能实体
- **逻辑**: 
  - 根据技能类型自动设置激活状态
  - 职业基础技能、禅、道、天赋技能默认非激活状态
  - 其他技能默认激活状态
  - 更新技能映射表和ID列表

---

## 2. 经验管理功能 (SkillComponentSystem)

### 扩展方法

#### AddBaseSkillExp (通过枚举)
- **签名**: `public static void AddBaseSkillExp(this SkillComponent self, SkillIdEnum skillIdEnum, long needAddExp, bool canMultiLevel = false, bool isSysChat = true)`
- **功能**: 通过技能枚举ID增加基础技能经验
- **参数**:
  - `self`: 技能组件
  - `skillIdEnum`: 技能ID枚举
  - `needAddExp`: 需要增加的经验值
  - `canMultiLevel`: 是否允许多级升级(可选，默认false)
  - `isSysChat`: 是否系统聊天提示(可选，默认true)
- **特性**: 先获取技能对象，再调用详细版本

#### AddBaseSkillExp (通过技能对象)
- **签名**: `public static void AddBaseSkillExp(this SkillComponent self, Skill skill, long needAddExp, bool canMultiLevel = false, bool isSysChat = true)`
- **功能**: 为指定技能增加基础经验的核心方法
- **参数**:
  - `self`: 技能组件
  - `skill`: 目标技能
  - `needAddExp`: 需要增加的经验值
  - `canMultiLevel`: 是否允许多级升级
  - `isSysChat`: 聊天消息类型
- **逻辑**:
  - 检查等级限制(最高99级或用户等级限制)
  - 处理经验分配和等级提升
  - 自动发送聊天通知和技能更新消息
  - 升级时重新计算用户属性并发送更新

#### AddLifeSkillExp (通过技能对象)
- **签名**: `public static long AddLifeSkillExp(this SkillComponent self, Skill skill, long addExp)`
- **功能**: 增加生活技能经验的高级接口
- **参数**:
  - `self`: 技能组件
  - `skill`: 目标生活技能
  - `addExp`: 基础经验值
- **返回**: 实际增加的经验值
- **特性**:
  - 智能处理护符加成效果
  - 针对禅、道、打工技能的特殊护符检测
  - 自动计算经验倍数加成
  - 支持多级升级

#### AddLifeSkillExp (通过枚举)
- **签名**: `public static long AddLifeSkillExp(this SkillComponent self, SkillIdEnum skillIdEnum, long addExp)`
- **功能**: 通过技能枚举ID增加生活技能经验
- **参数**:
  - `self`: 技能组件
  - `skillIdEnum`: 技能ID枚举
  - `addExp`: 基础经验值
- **返回**: 实际增加的经验值

---

## 3. 快捷栏管理功能 (SkillComponentSystem)

### 扩展方法

#### RemoveQuickFood
- **签名**: `public static bool RemoveQuickFood(this SkillComponent self, long thingId)`
- **功能**: 从快捷栏中移除指定的食物道具
- **参数**:
  - `self`: 技能组件
  - `thingId`: 要移除的物品ID
- **返回**: bool值，true表示移除成功
- **用途**: 当道具被删除时，同步清理快捷栏引用

---

## 4. 技能解析功能 (SkillManageComponentSystem)

### 扩展方法

#### Awake
- **签名**: `public static void Awake(this SkillManageComponent self)`
- **功能**: 技能管理组件的初始化方法
- **参数**: `self`: 技能管理组件
- **特性**: 系统自动调用的初始化方法

#### ParseBaseSkill
- **签名**: `public static List<Skill> ParseBaseSkill(this SkillManageComponent self, BaseSkill baseSkill)`
- **功能**: 解析基础技能配置生成技能列表
- **参数**:
  - `self`: 技能管理组件
  - `baseSkill`: 基础技能配置
- **返回**: 解析生成的技能列表
- **逻辑**:
  - 职业基础技能: 生成99级技能，每级伤害递增
  - 其他技能: 按配置信息生成多级技能
  - 自动设置技能属性(伤害、消耗、冷却等)

---

## 核心数据结构

- `SkillComponent`: 技能组件，存储用户技能信息
- `SkillManageComponent`: 技能管理组件，处理技能配置解析
- `Skill`: 技能实体，包含等级、经验、属性等信息
- `SkillIdEnum`: 技能ID枚举
- `BaseSkill`: 基础技能配置
- `SkillInfo`: 技能信息配置
- `SkillTypeEnum`: 技能类型枚举

## 技能类型说明

- `JOB_Base_SKILL`: 职业基础技能(可激活/关闭)
- `BASE_SKILL`: 基础技能
- `JOB_SKILL`: 职业技能
- `TALENT_SKILL`: 天赋技能(可激活/关闭)
- `TALENT_ACTIVE`: 天赋主动技能
- `LIFE_SKILL`: 生活技能

---

## 使用建议

### 推荐的操作流程:
1. **学习技能**: 先检查前置条件，再调用`AddLearnSkill`
2. **增加经验**: 
   - 基础/职业技能使用`AddBaseSkillExp`
   - 生活技能使用`AddLifeSkillExp`(自动处理护符加成)
3. **技能配置**: 使用`ParseBaseSkill`解析配置生成技能

### 性能考虑:
- 经验增加会触发属性重算，避免频繁调用
- 生活技能经验增加已优化护符检测逻辑
- 技能学习后会自动处理激活状态和快捷栏更新

### 特殊机制:
- 职业基础技能、禅道技能同时只能激活一个
- 生活技能经验受护符影响，有特殊的加成计算
- 技能等级受用户等级限制，最高99级 