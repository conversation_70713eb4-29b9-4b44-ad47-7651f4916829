# 技能系统快速参考 (For AI)

## 技能学习管理
```csharp
// 学习技能
void AddLearnSkill(this SkillComponent self, SkillIdEnum skillIdEnum)
void AddLearnSkill(this SkillComponent self, Skill skill)
```

## 经验管理
```csharp
// 基础技能经验
void AddBaseSkillExp(this SkillComponent self, SkillIdEnum skillIdEnum, long needAddExp, bool canMultiLevel = false, bool isSysChat = true)
void AddBaseSkillExp(this SkillComponent self, Skill skill, long needAddExp, bool canMultiLevel = false, bool isSysChat = true)

// 生活技能经验(含护符加成)
long AddLifeSkillExp(this SkillComponent self, Skill skill, long addExp)
long AddLifeSkillExp(this SkillComponent self, SkillIdEnum skillIdEnum, long addExp)
```

## 快捷栏管理
```csharp
// 移除快捷栏食物
bool RemoveQuickFood(this SkillComponent self, long thingId)
```

## 技能配置解析
```csharp
// 初始化
void Awake(this SkillManageComponent self)

// 解析基础技能配置
List<Skill> ParseBaseSkill(this SkillManageComponent self, BaseSkill baseSkill)
```

## 核心数据结构
- `SkillComponent`: 技能组件，管理用户技能
- `SkillManageComponent`: 技能管理组件，处理配置
- `Skill`: 技能实体(等级、经验、属性)
- `SkillIdEnum`: 技能ID枚举
- `BaseSkill`: 基础技能配置
- `SkillTypeEnum`: 技能类型枚举

## 技能类型
- `JOB_Base_SKILL`: 职业基础技能(可激活/关闭)
- `BASE_SKILL`: 基础技能
- `JOB_SKILL`: 职业技能  
- `TALENT_SKILL`: 天赋技能(可激活/关闭)
- `TALENT_ACTIVE`: 天赋主动技能
- `LIFE_SKILL`: 生活技能

## 常用操作模式
1. **学习技能**: 检查前置条件 → `AddLearnSkill`
2. **基础技能升级**: `AddBaseSkillExp`
3. **生活技能升级**: `AddLifeSkillExp` (自动护符加成)
4. **配置解析**: `ParseBaseSkill` 生成技能列表 