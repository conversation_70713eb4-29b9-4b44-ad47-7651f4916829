# 技能系统文档说明

## 文档目的
这些文档专门为AI阅读设计，旨在帮助AI理解和使用技能系统的接口。

## 文档结构

### SkillSystemsAPI.md
- **完整的API接口文档**
- 包含所有方法的详细说明、参数、返回值和使用特性
- 按功能分类：学习管理、经验管理、快捷栏管理、配置解析
- 区分扩展方法和静态方法
- 包含技能类型说明和使用建议

### QuickReference.md  
- **快速参考手册**
- 仅包含最重要的方法签名
- 核心数据结构说明
- 技能类型概览
- 常用操作模式

## 使用建议
1. 初次了解系统时阅读 `SkillSystemsAPI.md`
2. 日常开发时参考 `QuickReference.md`
3. 所有方法都是技能组件的扩展方法
4. 生活技能和基础技能使用不同的经验增加方法
5. 注意技能类型的激活规则和限制

## 系统特点
- **双组件架构**: SkillComponent负责运行时管理，SkillManageComponent负责配置解析
- **智能经验系统**: 生活技能自动处理护符加成，基础技能支持等级限制
- **激活机制**: 某些技能类型有互斥激活规则
- **等级限制**: 技能等级受用户等级限制，最高99级 