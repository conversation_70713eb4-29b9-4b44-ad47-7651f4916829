
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  public class GlobalInfoCache : Singleton<GlobalInfoCache>, ISingletonAwake
  {
    public ConcurrentDictionary<long, User> allOnlineUserCache = new();
    private ConcurrentDictionary<long, Account> allOnlineAccountCache = new();
    public ConcurrentDictionary<string, EntityRef<MapNode>> allMapNodeCache = new();
    private ConcurrentDictionary<long, EntityRef<MonsterInfo>> allMonsterInfoCache = new();
    private ConcurrentDictionary<NpcNameEnum, List<EntityRef<NpcInfo>>> allNpcInfoCache = new();
    private ConcurrentDictionary<long, EntityRef<NpcInfo>> allNpcInfoIdCache = new();
    public ConcurrentDictionary<long, EntityRef<AttackInCache>> allAttackInCache = new();
    private ConcurrentDictionary<ThingNameEnum, Thing> allBaseThingCache = new();
    private ConcurrentDictionary<MonBaseType, BaseMonster> allBaseMonsterCache = new();
    private ConcurrentDictionary<long, BaseAttack> soliderBaseAttackCache = new();
    private ConcurrentDictionary<long, BaseAttack> magicBaseAttackCache = new();
    public ConcurrentDictionary<SkillIdEnum, BaseSkill> baseSkillCache = new();
    public ConcurrentDictionary<SkillIdEnum, List<Skill>> allSkillCache = new();
    public ConcurrentDictionary<TaskIdEnum, BaseTask> allBaseTaskCache = new();
    public List<StartSceneConfig> mapScenes = new();
    public List<StartSceneConfig> attackScenes = new();
    public ConcurrentDictionary<string, DijkstraInfo> dijkstraInfoMap = new();
    public ConcurrentDictionary<string, DijkstraInfo> dijkstraInfoForCity = new();
    public ConcurrentDictionary<string, List<string>> mapPoints = new(); // 每个地图中包含的全部节点
    public ConcurrentDictionary<string, MallShopInfo> allMallShopCache = new();
    public ConcurrentDictionary<string, ComShopInfo> allComShopCache = new();
    public ConcurrentDictionary<long, TeamInfo> allTeamCache = new();
    public ConcurrentDictionary<long, GuildInfo> allGuildCache = new();
    public ConcurrentDictionary<long, UserFriendInfo> allUserFriendInfoCache = new();
    public ConcurrentDictionary<CraftingRecipeIdEnum, CraftingRecipe> allCraftingRecipeCache = new();
    public ConcurrentDictionary<ForgingRecipeIdEnum, ForgingRecipe> allForgingRecipeCache = new();
    public Config Config; // 全局配置
    public ActConfig ActConfig; // 活动配置
    public EntityRef<DaTaoShaActComp> daTaoShaActComp; // 大逃杀活动组件

    public void Awake()
    {
    }

    public Account GetOnlineAccount(long accountId)
    {
      this.allOnlineAccountCache.TryGetValue(accountId, out Account account);
      return account;
    }

    public User GetOnlineUser(long userId)
    {
      this.allOnlineUserCache.TryGetValue(userId, out User user);
      return user;
    }

    public void AddOnlineUser(long userId, User user)
    {
      this.allOnlineUserCache.AddOrUpdate(userId, user, (k, v) => user);
    }

    public void AddOnlineAccount(long accountId, Account account)
    {
      this.allOnlineAccountCache.AddOrUpdate(accountId, account, (k, v) => account);
    }

    public void RemoveOnlineUser(long userId)
    {
      this.allOnlineUserCache.TryRemove(userId, out _);
    }

    public void RemoveOnlineAccount(long accountId)
    {
      this.allOnlineAccountCache.TryRemove(accountId, out _);
    }

    public AttackComponent GetFightAttackComponent(FightInfo fightInfo)
    {
      if (fightInfo.liveType == LiveType.ROLE)
      {
        return this.GetOnlineUser(fightInfo.fightId)?.GetComponent<AttackComponent>();
      }
      else if (fightInfo.liveType == LiveType.MONSTER)
      {
        return this.GetMonsterInfo(fightInfo.fightId)?.GetComponent<AttackComponent>();
      }
      return null;
    }

    public void AddMapNode(string mapName, string pointName, EntityRef<MapNode> mapNode)
    {
      this.allMapNodeCache.AddOrUpdate(mapName + "_" + pointName, mapNode, (k, v) => mapNode);
    }

    public MapNode GetMapNode(string mapName, string pointName)
    {
      if (this.allMapNodeCache.TryGetValue(mapName + "_" + pointName, out EntityRef<MapNode> mapNode))
      {
        return mapNode;
      }
      return null;
    }

    public int GetMapNodeCount()
    {
      return this.allMapNodeCache.Count;
    }

    public void AddMonsterInfo(long monsterId, EntityRef<MonsterInfo> monsterInfo)
    {
      this.allMonsterInfoCache.AddOrUpdate(monsterId, monsterInfo, (k, v) => monsterInfo);
    }

    public MonsterInfo GetMonsterInfo(long monsterId)
    {
      this.allMonsterInfoCache.TryGetValue(monsterId, out EntityRef<MonsterInfo> monsterInfo);
      return monsterInfo;
    }

    public void RemoveMonsterInfo(long monsterId)
    {
      this.allMonsterInfoCache.TryRemove(monsterId, out _);
    }

    public void AddNpcInfo(NpcInfo npcInfo)
    {
      this.allNpcInfoCache.AddOrUpdate(npcInfo.name, [npcInfo], (k, v) => [.. v, npcInfo]);
      this.allNpcInfoIdCache.AddOrUpdate(npcInfo.Id, npcInfo, (k, v) => npcInfo);
    }

    public NpcInfo GetNpcInfo(long npcId)
    {
      this.allNpcInfoIdCache.TryGetValue(npcId, out EntityRef<NpcInfo> npcInfo);
      return npcInfo;
    }

    public List<NpcInfo> GetNpcInfo(NpcNameEnum npcName)
    {
      this.allNpcInfoCache.TryGetValue(npcName, out List<EntityRef<NpcInfo>> npcInfos);
      List<NpcInfo> npcInfoList = [.. npcInfos];
      return npcInfoList;
    }

    public void RemoveAllUser()
    {
      this.allOnlineUserCache.Clear();
    }

    public void RemoveAllAccount()
    {
      this.allOnlineAccountCache.Clear();
    }

    public void RemoveAllMapNode()
    {
      this.allMapNodeCache.Clear();
    }

    public void RemoveAllMonsterInfo()
    {
      this.allMonsterInfoCache.Clear();
    }

    public void RemoveAllNpcInfo()
    {
      this.allNpcInfoCache.Clear();
    }

    public void AddAttackInCache(long attackId, EntityRef<AttackInCache> attackInCache)
    {
      this.allAttackInCache.AddOrUpdate(attackId, attackInCache, (k, v) => attackInCache);
    }

    public AttackInCache GetAttackInCache(long attackId)
    {
      this.allAttackInCache.TryGetValue(attackId, out EntityRef<AttackInCache> attackInCache);
      return attackInCache;
    }

    public void RemoveAttackInCache(long attackId)
    {
      this.allAttackInCache.TryRemove(attackId, out _);
    }

    public void AddThing(ThingNameEnum thingName, Thing thing)
    {
      this.allBaseThingCache.AddOrUpdate(thingName, thing, (k, v) => thing);
    }

    public Thing GetBaseThing(ThingNameEnum thingName, bool isClone = false)
    {
      this.allBaseThingCache.TryGetValue(thingName, out Thing thing);
      if (isClone)
      {
        return thing.Clone() as Thing;
      }
      return thing;
    }

    public Dictionary<ThingNameEnum, Thing> GetBaseThingList(List<ThingNameEnum> thingNames, bool isClone = false)
    {
      Dictionary<ThingNameEnum, Thing> things = new Dictionary<ThingNameEnum, Thing>();
      foreach (ThingNameEnum thingName in thingNames)
      {
        this.allBaseThingCache.TryGetValue(thingName, out Thing thing);
        if (thing != null && !things.ContainsKey(thingName))
        {
          if (isClone)
          {
            things.Add(thingName, thing.Clone() as Thing);
          }
          else
          {
            things.Add(thingName, thing);
          }
        }
      }
      return things;
    }

    public void AddBaseMonster(MonBaseType monBaseType, BaseMonster baseMonster)
    {
      this.allBaseMonsterCache.AddOrUpdate(monBaseType, baseMonster, (k, v) => baseMonster);
    }

    public BaseMonster GetBaseMonster(MonBaseType monBaseType, bool isClone = false)
    {
      this.allBaseMonsterCache.TryGetValue(monBaseType, out BaseMonster baseMonster);
      if (isClone)
      {
        return baseMonster.Clone() as BaseMonster;
      }
      return baseMonster;
    }

    public void AddBaseAttack(BaseJob baseJob, BaseAttack baseAttack)
    {
      if (baseJob == BaseJob.SOLDIER)
      {
        this.soliderBaseAttackCache.AddOrUpdate(baseAttack.level, baseAttack, (k, v) => baseAttack);
      }
      else if (baseJob == BaseJob.MAGIC)
      {
        this.magicBaseAttackCache.AddOrUpdate(baseAttack.level, baseAttack, (k, v) => baseAttack);
      }
    }

    public BaseAttack GetBaseAttack(BaseJob baseJob, long level)
    {
      if (baseJob == BaseJob.SOLDIER)
      {
        this.soliderBaseAttackCache.TryGetValue(level, out BaseAttack baseAttack);
        return baseAttack;
      }
      else if (baseJob == BaseJob.MAGIC)
      {
        this.magicBaseAttackCache.TryGetValue(level, out BaseAttack baseAttack);
        return baseAttack;
      }
      return null;
    }

    public void AddSkill(SkillIdEnum skillId, List<Skill> skills)
    {
      this.allSkillCache.AddOrUpdate(skillId, skills, (k, v) => skills);
    }

    public Skill GetSkill(SkillIdEnum skillId, int level)
    {
      if (!this.allSkillCache.TryGetValue(skillId, out List<Skill> skills))
      {
        return null;
      }
      if (level < 0)
      {
        return null;
      }
      else if (level > skills.Count - 1)
      {
        return null;
      }
      return skills[level].Clone() as Skill;
    }

    public void AddBaseTask(TaskIdEnum taskId, BaseTask baseTask)
    {
      this.allBaseTaskCache.AddOrUpdate(taskId, baseTask, (k, v) => baseTask);
    }

    public BaseTask GetBaseTask(TaskIdEnum taskId)
    {
      this.allBaseTaskCache.TryGetValue(taskId, out BaseTask baseTask);
      return baseTask;
    }

    // 公会相关方法
    public void AddGuild(long guildId, GuildInfo guildInfo)
    {
      this.allGuildCache.AddOrUpdate(guildId, guildInfo, (k, v) => guildInfo);
    }

    public GuildInfo GetGuild(long guildId)
    {
      this.allGuildCache.TryGetValue(guildId, out GuildInfo guildInfo);
      return guildInfo;
    }

    public GuildInfo GetGuildByName(string guildName)
    {
      if (string.IsNullOrEmpty(guildName))
      {
        return null;
      }

      foreach (var kvp in this.allGuildCache)
      {
        if (kvp.Value.name.Equals(guildName, StringComparison.OrdinalIgnoreCase))
        {
          return kvp.Value;
        }
      }
      return null;
    }

    public void RemoveGuild(long guildId)
    {
      this.allGuildCache.TryRemove(guildId, out _);
    }

    public List<GuildInfo> GetAllGuilds()
    {
      return this.allGuildCache.Values.ToList();
    }

    public bool IsGuildNameExists(string guildName)
    {
      return GetGuildByName(guildName) != null;
    }

    public int GetGuildCount()
    {
      return this.allGuildCache.Count;
    }
  }
}