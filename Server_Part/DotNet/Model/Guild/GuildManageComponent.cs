using System.Collections.Generic;

namespace MaoYouJi
{
  /// <summary>
  /// 公会管理组件 - 全局公会管理
  /// </summary>
  [ComponentOf]
  public class GuildManageComponent : Entity, IAwake
  {
    /// <summary>
    /// 公会分页大小
    /// </summary>
    public const int GUILD_PAGE_SIZE = 10;
    
    /// <summary>
    /// 公会邀请过期时间（毫秒）- 7天
    /// </summary>
    public const long GUILD_INVITE_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000L;
    
    /// <summary>
    /// 公会联盟邀请过期时间（毫秒）- 3天
    /// </summary>
    public const long GUILD_ALLIANCE_INVITE_EXPIRE_TIME = 3 * 24 * 60 * 60 * 1000L;
    
    /// <summary>
    /// 公会名称最小长度
    /// </summary>
    public const int GUILD_NAME_MIN_LENGTH = 2;
    
    /// <summary>
    /// 公会名称最大长度
    /// </summary>
    public const int GUILD_NAME_MAX_LENGTH = 20;
    
    /// <summary>
    /// 公会描述最大长度
    /// </summary>
    public const int GUILD_DESC_MAX_LENGTH = 200;
    
    /// <summary>
    /// 公会公告最大长度
    /// </summary>
    public const int GUILD_ANNOUNCEMENT_MAX_LENGTH = 500;
    
    /// <summary>
    /// 创建公会所需金币数量
    /// </summary>
    public const long CREATE_GUILD_COST_COIN = 100000;
  }
}
